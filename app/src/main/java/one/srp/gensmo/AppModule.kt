package one.srp.gensmo

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import one.srp.core.analytics.AnalyticsManager
import one.srp.core.analytics.providers.MetricApiAnalyticsProvider
import one.srp.gensmo.utils.metrics.MetricManager
import javax.inject.Qualifier
import javax.inject.Singleton

@Qualifier
@Retention(AnnotationRetention.RUNTIME)
annotation class ApplicationScope

/**
 * Hilt module for analytics dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object AppModule {
    @Provides
    @Singleton
    @ApplicationScope
    fun provideAppScope(): CoroutineScope =
        CoroutineScope(SupervisorJob() + Dispatchers.Default)

    @Provides
    @Singleton
    fun provideMetricManager(
        @ApplicationScope scope: CoroutineScope,
        analyticsManager: AnalyticsManager,
        metricApiAnalyticsProvider: MetricApiAnalyticsProvider,
    ): MetricManager = MetricManager(
        scope, analyticsManager, metricApiAnalyticsProvider
    )
}
