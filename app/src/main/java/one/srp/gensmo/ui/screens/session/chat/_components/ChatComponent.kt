package one.srp.gensmo.ui.screens.session.chat._components

import androidx.compose.runtime.Composable
import one.srp.gensmo.data.model.ChatMessage
import one.srp.gensmo.data.model.SearchLoadingMessage
import one.srp.gensmo.data.model.SearchQueryMessage
import one.srp.gensmo.data.model.SearchResMessage
import one.srp.gensmo.data.model.TryOnChangebgLoadingMessage
import one.srp.gensmo.data.model.TryOnChangebgResMessage
import one.srp.gensmo.data.model.TryOnLoadingMessage
import one.srp.gensmo.data.model.TryOnQueryMessage
import one.srp.gensmo.data.model.TryOnResMessage
import one.srp.gensmo.data.model.UnknownMessage
import one.srp.gensmo.ui.screens.session.chat._components.message.SearchLoadingComponent
import one.srp.gensmo.ui.screens.session.chat._components.message.SearchQueryComponent
import one.srp.gensmo.ui.screens.session.chat._components.message.SearchResComponent
import one.srp.gensmo.ui.screens.session.chat._components.message.TryOnChangebgLoadingComponent
import one.srp.gensmo.ui.screens.session.chat._components.message.TryOnChangebgResComponent
import one.srp.gensmo.ui.screens.session.chat._components.message.TryOnLoadingComponent
import one.srp.gensmo.ui.screens.session.chat._components.message.TryOnQueryComponent
import one.srp.gensmo.ui.screens.session.chat._components.message.TryOnResComponent
import one.srp.gensmo.ui.screens.session.chat._components.message.UnknownComponent


@Composable
fun ChatComponent(
    item: ChatMessage,
    onMessage: (ChatMessage) -> Unit = {},
    onNavigate: (SessionNavigation, ChatMessage, Int?) -> Unit = { _, _, _ -> },
) {
    when (item) {
        is UnknownMessage -> {
            UnknownComponent(item)
        }

        is SearchQueryMessage -> {
            SearchQueryComponent(item, onMessage)
        }

        is SearchLoadingMessage -> {
            SearchLoadingComponent(item)
        }

        is SearchResMessage -> {
            SearchResComponent(item, onMessage, onNavigate)
        }

        is TryOnQueryMessage -> {
            TryOnQueryComponent(item)
        }

        is TryOnLoadingMessage -> {
            TryOnLoadingComponent(item)
        }

        is TryOnResMessage -> {
            TryOnResComponent(item, onMessage, onNavigate)
        }

        is TryOnChangebgLoadingMessage->{
            TryOnChangebgLoadingComponent(item, onMessage, onNavigate)
        }

        is TryOnChangebgResMessage -> {
            TryOnChangebgResComponent(item, onMessage, onNavigate)
        }

        else -> {}
    }
}

enum class SessionNavigation {
    CollageTask, TryOnTask
}

