package one.srp.gensmo.ui.screens.session._components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.ui.theme.appThemeShapes

@Composable
fun SecondaryIconTextButton(
    onClick: () -> Unit,
    icon: @Composable () -> Unit,
    text: String,
    modifier: Modifier = Modifier,
) {
    Button(
        onClick = onClick,
        colors = ButtonDefaults.buttonColors(
            MaterialTheme.colorScheme.surface, MaterialTheme.colorScheme.onSurface
        ),
        shape = appThemeShapes.small,
        contentPadding = PaddingValues(start = 4.dp, top = 4.dp, end = 8.dp, bottom = 4.dp),
        modifier = modifier.height(32.dp)
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(4.dp),
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxHeight()
        ) {
            Box(
                modifier = Modifier
                    .size(24.dp)
                    .padding(3.dp)
            ) {
                icon()
            }

            Text(text, style = AppThemeTextStyle.Body13H)
        }
    }
}

