package one.srp.gensmo.ui.screens.tryon.create

import android.widget.Toast
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.layout.layout
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import one.srp.core.analytics.events.MetricEvent
import one.srp.core.analytics.hooks.rememberMetricHelper
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.gensmo.ui.components.notification.ToastManager
import one.srp.gensmo.ui.components.notification.ToastType
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.tryon.create._components.AdditionalDetail
import one.srp.gensmo.ui.screens.tryon.create._components.BodyShapeOption
import one.srp.gensmo.ui.screens.tryon.create._components.BodyShapeSelector
import one.srp.gensmo.ui.screens.tryon.create._components.BodyTypeOption
import one.srp.gensmo.ui.screens.tryon.create._components.BodyTypeSelector
import one.srp.gensmo.ui.screens.tryon.create._components.CameraPickerFace
import one.srp.gensmo.ui.screens.tryon.create._components.Intro
import one.srp.gensmo.ui.screens.tryon.create._components.LoginModal
import one.srp.gensmo.ui.screens.tryon.create._components.PrivacyModal
import one.srp.gensmo.ui.screens.tryon.create._components.Selfie
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.viewmodel.tryon.CreateViewModel
import timber.log.Timber


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TryOnCreateScreen(
    navActions: NavActions,
    source: String,
    createViewModel: CreateViewModel = hiltViewModel(),
) {
    val uiState by createViewModel.uiState.collectAsState()
    var context = LocalContext.current
    val metric = rememberMetricHelper(EventRefer.CreateReplica)

    LaunchedEffect(Unit) {
        metric(
            MetricEvent.SelectItem(
                itemListName = EventItemListName.Screen,
                method = EventMethod.PageView,
                actionType = EventActionType.Default
            )
        )
    }

    BackHandler(
        enabled = uiState.currentProgress >= 0.5f,
        onBack = {
            createViewModel.updateProgress(uiState.currentProgress - 0.5f)
        }
    )

    Scaffold(
        topBar = {
            Column {
                CenterAlignedTopAppBar(
                    title = {
                        Column {
                            LinearProgressIndicator(
                                progress = { if (uiState.currentProgress < 0.5f) 0.5f else uiState.currentProgress },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(2.dp),
                                color = Color(0xFF222222),
                                trackColor = Color(0xFFD9D9D9),
                                strokeCap = StrokeCap.Butt,
                                drawStopIndicator = {},
                            )
                        }
                    },
                    navigationIcon = {
                        IconButton(
                            onClick = {
                                when {
                                    uiState.currentProgress >= 0.5f -> {
                                        createViewModel.updateProgress(uiState.currentProgress - 0.5f)
                                    }

                                    else -> {
                                        if (!uiState.isBackButtonDisabled) {
                                            createViewModel.updateBackButtonState(true)
                                            navActions.back()
                                            createViewModel.enableBackButtonAfterDelay()
                                        }
                                    }
                                }
                            },
                            enabled = !uiState.isBackButtonDisabled
                        ) {
                            Icon(
                                Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "返回"
                            )
                        }
                    },
                    actions = {
                        IconButton(onClick = { createViewModel.updatePrivacyDialog(true) }) {
                            Icon(
                                painter = painterResource(id = R.drawable.icon_shield),
                                contentDescription = "保护"
                            )
                        }
                    },
                    colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                        containerColor = Color(0xFFF5F5F5)
                    )
                )
            }
        }
    ) { paddingValues ->
        Surface(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            color = Color.White
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {

                when {
                    uiState.currentProgress < 0.5f -> {
                        Intro()
                        Spacer(modifier = Modifier.weight(1f))
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 8.dp)
                                .layout { measurable, constraints ->
                                    val placeable = measurable.measure(
                                        constraints.copy(
                                            maxWidth = constraints.maxWidth + 32.dp.roundToPx()
                                        )
                                    )
                                    layout(placeable.width, placeable.height) {
                                        placeable.placeRelative(0, 0)
                                    }
                                },
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Button(
                                onClick = {
                                    if (createViewModel.isUserLoggedIn()) {
                                        metric(
                                            MetricEvent.SelectItem(
                                                itemListName = EventItemListName.UseDefaultAvatarBtn,
                                                method = EventMethod.Click,
                                                actionType = EventActionType.CreateReplica
                                            )
                                        )
                                        navActions.navigateToTryOnModel(setFace = true)
                                    } else {
                                        metric(
                                            MetricEvent.SelectItem(
                                                itemListName = EventItemListName.UseDefaultAvatarBtn,
                                                method = EventMethod.Click,
                                                actionType = EventActionType.LoginBlock
                                            )
                                        )
                                        createViewModel.updateLoginDialog(true)
                                        createViewModel.setLoginSource("default_avatar")
                                    }
                                },
                                modifier = Modifier
                                    .weight(1f)
                                    .background(
                                        color = Color(0xFFF5F5F5),
                                        shape = RoundedCornerShape(size = 4.dp)
                                    ),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFFF5F5F5),
                                    contentColor = Color(0xFF222222)
                                ),
                                contentPadding = PaddingValues(horizontal = 16.dp, vertical = 0.dp)
                            ) {
                                Text(
                                    "Use default Avatar",
                                    style = AppThemeTextStyle.Body16H,
                                    textAlign = TextAlign.Center
                                )
                            }

                            Button(
                                onClick = {
                                    if (createViewModel.isUserLoggedIn()) {
                                        metric(
                                            MetricEvent.SelectItem(
                                                itemListName = EventItemListName.UploadSelfieCtaBtn,
                                                method = EventMethod.Click,
                                                actionType = EventActionType.CreateReplica
                                            )
                                        )
                                        createViewModel.updateCameraPicker(true)
                                    } else {
                                        metric(
                                            MetricEvent.SelectItem(
                                                itemListName = EventItemListName.UploadSelfieCtaBtn,
                                                method = EventMethod.Click,
                                                actionType = EventActionType.LoginBlock
                                            )
                                        )
                                        createViewModel.updateLoginDialog(true)
                                        createViewModel.setLoginSource("upload_selfie")
                                    }
                                },
                                modifier = Modifier
                                    .weight(1f)
                                    .background(
                                        color = Color(0xFF222222),
                                        shape = RoundedCornerShape(size = 4.dp)
                                    ),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFF222222),
                                    contentColor = Color.White
                                ),
                                contentPadding = PaddingValues(horizontal = 16.dp, vertical = 0.dp)
                            ) {
                                Text(
                                    "Upload selfie +",
                                    style = AppThemeTextStyle.Body16H,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                    }

                    uiState.currentProgress < 1.0f -> {
                        Column(
                            modifier = Modifier.fillMaxSize()
                        ) {
                            Selfie(
                                uri = uiState.selectedImageUri ?: "",
                                onImageSelected = { uri ->
                                    // 只有当图片验证未完成时才触发上传
                                    if (!uiState.isPhotoUploadedAndValidatedCompleted) {
                                        createViewModel.validateImageByMLKit(
                                            uri,
                                            onSuccess = {
                                                createViewModel.uploadImage(uri) { errorMessage ->
                                                    Toast.makeText(
                                                        context,
                                                        errorMessage,
                                                        Toast.LENGTH_SHORT
                                                    ).show()
                                                }
                                            },
                                            onError = { errorMessage ->
                                                Toast.makeText(
                                                    context,
                                                    errorMessage,
                                                    Toast.LENGTH_SHORT
                                                ).show()
                                            }
                                        )
                                    } else {
                                        // 如果已验证完成但选择了新图片，更新当前选择的图片并重置验证状态
                                        if (uri.toString() != uiState.selectedImageUri) {
                                            createViewModel.setSelectedImage(uri.toString())
                                            createViewModel.validateImageByMLKit(
                                                uri,
                                                onSuccess = {
                                                    createViewModel.uploadImage(uri) { errorMessage ->
                                                        Toast.makeText(
                                                            context,
                                                            errorMessage,
                                                            Toast.LENGTH_SHORT
                                                        ).show()
                                                    }
                                                },
                                                onError = { errorMessage ->
                                                    Toast.makeText(
                                                        context,
                                                        errorMessage,
                                                        Toast.LENGTH_SHORT
                                                    ).show()
                                                }
                                            )
                                        }
                                    }
                                },
                                isPhotoUploadedAndValidatedCompleted = uiState.isPhotoUploadedAndValidatedCompleted,
                                modifier = Modifier.weight(1f)
                            )

                            Text(
                                text = "AI will help fill in the gaps from your photo, but you can change it anytime.",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(start = 16.dp, end = 16.dp, bottom = 8.dp),
                                textAlign = TextAlign.Center
                            )

                            Button(
                                onClick = {
                                    metric(
                                        MetricEvent.SelectItem(
                                            itemListName = EventItemListName.NextBtn,
                                            method = EventMethod.Click,
                                            actionType = EventActionType.Default
                                        )
                                    )
                                    createViewModel.updateProgress(1.0f)
                                },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(56.dp)
                                    .background(
                                        color = Color(0xFF222222),
                                        shape = RoundedCornerShape(size = 4.dp)
                                    ),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color.Transparent,
                                    contentColor = Color.White
                                ),
                                contentPadding = PaddingValues(
                                    horizontal = 24.dp,
                                    vertical = 16.dp
                                ),
                                enabled = uiState.isPhotoUploadedAndValidatedCompleted
                            ) {
                                Text("Next", style = AppThemeTextStyle.Body16H)
                            }
                        }
                    }

                    uiState.currentProgress == 1.0f -> {
                        Column(
                            modifier = Modifier.fillMaxSize()
                        ) {
                            Column(
                                modifier = Modifier
                                    .weight(1f)
                                    .verticalScroll(rememberScrollState())
                                    .padding(bottom = 16.dp)
                            ) {
                                BodyTypeSelector(
                                    selectedBodyType = BodyTypeOption.valueOf(uiState.bodyType),
                                    onBodyTypeSelected = { bodyType ->
                                        createViewModel.updateBodyType(bodyType.toString())
                                    }
                                )
                                Spacer(
                                    modifier = Modifier
                                        .height(18.dp)
                                        .fillMaxWidth()
                                )
                                BodyShapeSelector(
                                    selectedBodyShape = BodyShapeOption.valueOf(uiState.bodyShape),
                                    onBodyShapeSelected = { bodyShape ->
                                        createViewModel.updateBodyShape(bodyShape.toString())
                                    }
                                )
                                Spacer(
                                    modifier = Modifier
                                        .height(18.dp)
                                        .fillMaxWidth()
                                )
                                AdditionalDetail(
                                    additionalInfo = uiState.additionalInfo,
                                    onAdditionalInfoChanged = { info ->
                                        createViewModel.updateAdditionalInfo(info)
                                    }
                                )

                                Spacer(
                                    modifier = Modifier
                                        .height(18.dp)
                                        .fillMaxWidth()
                                )
                                Text(
                                    text = "Additional details",
                                    style = AppThemeTextStyle.Heading18D.copy(
                                        color = Color(0xFF000000),
                                        fontWeight = FontWeight(700)
                                    ),
                                    modifier = Modifier
                                        .padding(bottom = 8.dp)
                                        .align(Alignment.Start)
                                )
                                Text(
                                    text = "AI will help fill in the gaps from your photo, but you can change it anytime.",
                                    style = AppThemeTextStyle.Body12LightH.copy(
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFF495057)
                                    ),
                                    modifier = Modifier
                                        .padding(bottom = 16.dp)
                                        .align(Alignment.Start)
                                )
                            }

                            val coroutineScope = rememberCoroutineScope()
                            Button(
                                onClick = {
                                    metric(
                                        MetricEvent.SelectItem(
                                            itemListName = EventItemListName.AvatarGenerateBtn,
                                            method = EventMethod.Click,
                                            actionType = EventActionType.AvatarGen
                                        )
                                    )
                                    createViewModel.generateAvatar(
                                        onSuccess = {
                                            ToastManager.show(
                                                text = "Generating your Avatar...",
                                                subText = "About 3 minutes",
                                                type = ToastType.ICON,
                                                iconType = "avatar",
                                                onClick = {
                                                    navActions.navigateToCloset()
                                                }
                                            )
                                            navActions.back()
                                        },
                                        onError = {
                                            Timber.e("generateAvatar onError: $it")
                                            Toast.makeText(
                                                context,
                                                "Failed to generate avatar, please try again later",
                                                Toast.LENGTH_SHORT
                                            ).show()
                                            createViewModel.updateIsGenerating(false)
                                        }
                                    )
                                },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(56.dp)
                                    .background(
                                        color = Color(0xFF222222),
                                        shape = RoundedCornerShape(size = 4.dp)
                                    ),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color.Transparent,
                                    contentColor = Color.White
                                ),
                                contentPadding = PaddingValues(
                                    horizontal = 24.dp,
                                    vertical = 16.dp
                                ),
                                enabled = !uiState.isGenerating
                            ) {
                                if (uiState.isGenerating) {
                                    Row(
                                        horizontalArrangement = Arrangement.Center,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        CircularProgressIndicator(
                                            color = Color.White,
                                            modifier = Modifier.size(24.dp)
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                        Text("Generating", style = AppThemeTextStyle.Body16H)
                                    }
                                } else {
                                    Text("GENERATE", style = AppThemeTextStyle.Body16H)
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    if (uiState.showPrivacyDialog) {
        PrivacyModal(
            onDismiss = { createViewModel.updatePrivacyDialog(false) }
        )
    }

    if (uiState.showLoginDialog) {
        val context = LocalContext.current
        LoginModal(
            onDismiss = { createViewModel.updateLoginDialog(false) },
            isLoading = uiState.isLoading,
            onGoogleLogin = {
                createViewModel.loginWithGoogle(
                    context = context,
                    onSuccess = {
                        when (createViewModel.getLoginSource()) {
                            "default_avatar" -> navActions.navigateToTryOnModel(setFace = true)
                            "upload_selfie" -> createViewModel.updateCameraPicker(true)
                        }
                    }
                )
                createViewModel.updateLoginDialog(false)
            },
            onAppleLogin = {
                createViewModel.loginWithApple(
                    context = context,
                    onSuccess = {
                        when (createViewModel.getLoginSource()) {
                            "default_avatar" -> navActions.navigateToTryOnModel(setFace = true)
                            "upload_selfie" -> createViewModel.updateCameraPicker(true)
                        }
                    }
                )
                createViewModel.updateLoginDialog(false)
            }
        )
    }

    if (uiState.showCameraPicker) {
        CameraPickerFace(
            onPhotoTaken = { uri ->
                Timber.d("onPhotoTaken: $uri")
                createViewModel.updateCameraPicker(false)
                createViewModel.setSelectedImage(uri.toString())
                createViewModel.updateProgress(0.5f)
            },
            onMiss = {
                createViewModel.updateCameraPicker(false)
            }
        )
    }

    if (uiState.isLoading) {
        Box(
            modifier = Modifier
                .fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Surface(
                modifier = Modifier.fillMaxSize(),
                color = Color.Black.copy(alpha = 0.5f)
            ) {}

            Card(
                modifier = Modifier
                    .size(100.dp)
                    .padding(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
}

