package one.srp.gensmo.ui.screens.session.chat._viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import jakarta.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.Serializable
import okhttp3.Request
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.data.model.ChatJson
import one.srp.gensmo.data.model.ChatMessage
import one.srp.gensmo.data.model.ChatMessageRole
import one.srp.gensmo.data.model.SearchResMessage
import one.srp.gensmo.data.model.TryOnChangebgLoadingMessage
import one.srp.gensmo.data.model.TryOnChangebgQueryMessage
import one.srp.gensmo.data.model.TryOnChangebgResMessage
import one.srp.gensmo.data.model.TryOnResMessage
import one.srp.gensmo.data.remote.apis.SessionApi
import one.srp.gensmo.data.remote.utils.WebSocketConnectionStatus
import one.srp.gensmo.data.remote.utils.WebSocketManager
import one.srp.gensmo.data.store.DeviceDataStoreManager
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.utils.data.spliceList
import one.srp.gensmo.utils.env.EnvConf
import one.srp.gensmo.utils.env.MetaInfo
import one.srp.gensmo.utils.metrics.MetricData
import one.srp.gensmo.utils.metrics.MetricManager
import timber.log.Timber

@Serializable
data class ChatMeta(
    val title: String? = null,
    val sessionId: String? = null,
)

enum class SessionActionStatus {
    Idle, Loading, Success, Failed,
}

@Serializable
data class ChatLock(
    val input: Boolean = false,
    val changeBg: Boolean = false,
)

@HiltViewModel
class SessionChatViewModel @Inject constructor(
    private val sessionApi: SessionApi,
    private val metricManager: MetricManager,
) : ViewModel() {
    private val _chatMeta = MutableStateFlow(ChatMeta())
    val chatMeta = _chatMeta.asStateFlow()
    private val _chatList = MutableStateFlow<List<ChatMessage>>(emptyList())
    val chatList = _chatList.asStateFlow()

    private val _webSocketManager = WebSocketManager(viewModelScope)
    private val _connectStatus = MutableStateFlow(SessionActionStatus.Idle)
    val connectStatus = _connectStatus.asStateFlow()

    private val _initialMessage = MutableStateFlow<ChatMessage?>(null)
    val initialMessage = _initialMessage.asStateFlow()

    private val _initialSessionMeta = MutableStateFlow<ChatMeta?>(null)
    private val _restoreStatus = MutableStateFlow(SessionActionStatus.Idle)
    val restoreStatus = _restoreStatus.asStateFlow()

    private val _initStatus = MutableStateFlow(SessionActionStatus.Idle)
    val initStatus = _initStatus.asStateFlow()

    private val _chatLock = MutableStateFlow<ChatLock>(ChatLock())
    val chatLock = _chatLock.asStateFlow()

    private fun buildRequest(param: String? = null): Request {
        val url = "wss://gem-workflow.${EnvConf.serverHost}/ws/chat${param ?: ""}"
        val token = runBlocking { UserDataStoreManager.getToken() }
        val deviceId = runBlocking { DeviceDataStoreManager.getDeviceId() }

        return Request.Builder().apply {
            url(url)
            token?.let { header("Authorization", "Bearer $token") }

            header("f-version", MetaInfo.VERSION)
            header("f-source", MetaInfo.PLATFORM).header("Deviceid", deviceId)
            header("X-Gensmo-Active-Id", deviceId)
            header("X-Gensmo-Timestamp", (System.currentTimeMillis() * 1000).toString())
        }.build()
    }

    private fun buildParam(sessionId: String? = null): String {
        return sessionId?.let { "?session_id=$it" } ?: ""
    }

    fun connectServer(
        param: String? = null,
        onOpen: () -> Unit = {},
        onMessage: (String) -> Unit = { receiveMessage(it) },
        onError: (Throwable) -> Unit = {},
    ) {
        try {
            _webSocketManager.disconnect()

            if (_connectStatus.value != SessionActionStatus.Idle) {
                _connectStatus.value = SessionActionStatus.Idle
            }

            _webSocketManager.connectWebSocket(
                request = buildRequest(param),
                onMessage = onMessage,
                onOpen = onOpen,
                onError = onError,
            )
        } catch (e: Exception) {
            Timber.w(e)
        }
    }

    fun sendMessage(message: ChatMessage) {
        if (_chatLock.value.input) return

        _chatLock.value = _chatLock.value.copy(input = true)
        _chatMeta.value.sessionId?.let { sessionId ->
            val processed = if (message.sessionId == sessionId) {
                message
            } else {
                message.alterId(sessionId)
            }

            if (!checkCanSend(message)) {
                _chatLock.value = _chatLock.value.copy(input = false)
                return@let
            }
            preSendMessage(message)

            msgToStr(processed)?.let { str ->
                _chatList.value += processed

                _webSocketManager.sendMessage(
                    message = str, onFail = {
                        connectServer(
                            param = buildParam(sessionId),
                            onOpen = { _webSocketManager.sendMessage(str) },
                            onMessage = { receiveMessage(it) },
                            onError = { _connectStatus.value = SessionActionStatus.Failed }
                        )
                    })
            }
        }
    }

    fun receiveMessage(str: String) {
        _chatLock.value = _chatLock.value.copy(input = false)

        strToMsg(str)?.let { message ->
            preReceiveMessage(message)

            val findIndex = _chatList.value.indexOfFirst { it.messageId == message.messageId }
            if (findIndex != -1) {
                updateMessage(findIndex, message)
            } else {
                _chatList.value += message
            }

            postReceiveMessage(message)
        }
    }

    private fun strToMsg(str: String): ChatMessage? {
        return try {
            ChatJson.decodeFromString<ChatMessage>(str)
        } catch (e: Exception) {
            Timber.w(e)
            null
        }
    }

    private fun msgToStr(msg: ChatMessage): String? {
        return try {
            ChatJson.encodeToString(ChatMessage.serializer(), msg)
        } catch (e: Exception) {
            Timber.w(e)
            null
        }
    }

    private fun preSendMessage(message: ChatMessage) {
        when (message) {
            is TryOnChangebgQueryMessage -> {
                _chatLock.value = _chatLock.value.copy(changeBg = true)
            }

            else -> {}
        }
    }

    private fun preReceiveMessage(message: ChatMessage) {
        if (_chatMeta.value.sessionId == null) {
            when (message.role) {
                ChatMessageRole.Root.value -> {
                    _chatMeta.value = _chatMeta.value.copy(sessionId = message.sessionId)
                }
            }
        }

        when (message) {
            is TryOnChangebgLoadingMessage, is TryOnChangebgResMessage -> {
                if (_chatLock.value.changeBg) {
                    _chatLock.value = _chatLock.value.copy(changeBg = false)
                }
            }

            else -> {}
        }
    }

    private fun postReceiveMessage(message: ChatMessage) {
        when (message) {
            is SearchResMessage -> {
                viewModelScope.launch {
                    MetricData.logEventAF("af_collage_gen_complete")

                    metricManager.log(
                        SelectItem(
                            refer = EventRefer.Channel,
                            itemListName = EventItemListName.CollageList,
                            method = EventMethod.LoadComplete,
                            actionType = EventActionType.CollageGenComplete,
                            items = listOf(
                                EventItem(
                                    itemCategory = EventItemCategory.Channel,
                                    itemId = message.sessionId,
                                ), EventItem(
                                    itemCategory = EventItemCategory.CollageGenTask,
                                    itemId = message.value.searchRes.taskId,
                                    itemName = message.value.searchRes.reasoning,
                                )
                            )
                        )
                    )
                }
            }

            is TryOnResMessage -> {
                viewModelScope.launch {
                    MetricData.logEventAF("af_try_on_complete")

                    metricManager.log(
                        SelectItem(
                            refer = EventRefer.Channel,
                            itemListName = EventItemListName.TryOnResultList,
                            method = EventMethod.LoadComplete,
                            actionType = EventActionType.TryOnComplete,
                            items = listOf(
                                EventItem(
                                    itemCategory = EventItemCategory.Channel,
                                    itemId = message.sessionId,
                                ), EventItem(
                                    itemCategory = EventItemCategory.TryOnTask,
                                    itemId = message.value.tryonRes.tryOnTaskId,
                                )
                            )
                        )
                    )
                }
            }

            is TryOnChangebgResMessage -> {
                viewModelScope.launch {
                    metricManager.log(
                        SelectItem(
                            refer = EventRefer.Channel,
                            itemListName = EventItemListName.TryOnResultList,
                            method = EventMethod.LoadComplete,
                            actionType = EventActionType.TryOnComplete,
                            items = listOf(
                                EventItem(
                                    itemCategory = EventItemCategory.Channel,
                                    itemId = message.sessionId,
                                ), EventItem(
                                    itemCategory = EventItemCategory.TryOnTask,
                                    itemId = message.value.tryonRes.tryOnTaskId,
                                ), EventItem(
                                    itemCategory = EventItemCategory.TryOnScenarioCollage,
                                )
                            )
                        )
                    )
                }
            }

            else -> {}
        }

    }

    private fun checkCanSend(message: ChatMessage): Boolean {
        return when (message) {
            is TryOnChangebgQueryMessage -> {
                !_chatLock.value.changeBg
            }

            else -> true
        }
    }

    fun updateMessage(index: Int, message: ChatMessage) {
        _chatList.value = spliceList(_chatList.value, index, 1, message)
    }

    fun clear() {
        onCleared()
    }

    fun setInitialState(message: ChatMessage?) {
        _initialMessage.value = message
    }

    fun setTitle(title: String) {
        _chatMeta.value = _chatMeta.value.copy(title = title)
    }

    fun setInitialSessionId(sessionMeta: ChatMeta) {
        clear()
        _initialMessage.value = null
        _initialSessionMeta.value = sessionMeta
    }

    fun restoreSession(sessionId: String) {
        _restoreStatus.value = SessionActionStatus.Loading

        viewModelScope.launch {
            try {
                val res = sessionApi.getSessionMessages(sessionId, 50, 0)
                if (res.isSuccessful) {
                    res.body()?.messages?.let {
                        _chatList.value = it.reversed()
                        _restoreStatus.value = SessionActionStatus.Success
                    } ?: run {
                        _restoreStatus.value = SessionActionStatus.Failed
                    }
                } else {
                    _restoreStatus.value = SessionActionStatus.Failed
                }
            } catch (e: Exception) {
                Timber.w(e)
                _restoreStatus.value = SessionActionStatus.Failed
            }
        }

        _initialSessionMeta.value = null
    }

    fun initServer() {
        _initStatus.value = SessionActionStatus.Loading

        _initialSessionMeta.value?.let { meta ->
            meta.sessionId?.let {
                _chatMeta.value = meta
                restoreSession(it)
                connectServer(buildParam(it))
                _initStatus.value = SessionActionStatus.Success
            }
        } ?: run {
            when (_webSocketManager.connectionStatus.value) {
                WebSocketConnectionStatus.IDLE, WebSocketConnectionStatus.DISCONNECTED -> {
                    connectServer(onOpen = {
                        _initStatus.value = SessionActionStatus.Success
                    })
                }

                else -> {
                    _initStatus.value = SessionActionStatus.Success
                }
            }
        }
    }

    override fun onCleared() {
        _webSocketManager.clear()
        _chatMeta.value = ChatMeta()
        _chatList.value = emptyList()
        _chatLock.value = ChatLock()
        _restoreStatus.value = SessionActionStatus.Idle
        _connectStatus.value = SessionActionStatus.Idle
        _initStatus.value = SessionActionStatus.Idle
        super.onCleared()
    }
}
