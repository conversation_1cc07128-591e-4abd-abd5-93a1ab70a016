package one.srp.gensmo.utils.integration

import android.content.Context
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import com.google.firebase.Firebase
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.analytics
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.BuildConfig
import one.srp.gensmo.data.store.DeviceDataStoreManager
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.utils.metrics.AppsflyerManager
import one.srp.gensmo.utils.metrics.DeepLinkCallback
import one.srp.gensmo.utils.metrics.MetricData
import one.srp.gensmo.utils.metrics.MetricManager
import timber.log.Timber

fun initMetricSDKs(
    context: Context,
    packageManager: PackageManager,
    packageName: String,
    metricManager: MetricManager,
) {
    try {
        // Firebase
        initializeFirebase(packageManager, packageName, metricManager)

        // AppsFlyer
        initializeAppsflyer(context)
    } catch (e: Exception) {
        Timber.e(e)
    }
}

private fun initializeFirebase(
    packageManager: PackageManager,
    packageName: String,
    metricManager: MetricManager,
) {
    val firebaseAnalytics = Firebase.analytics
    firebaseAnalytics.setAnalyticsCollectionEnabled(true)

    val packageInfo = packageManager.getPackageInfo(packageName, 0)
    val launchBundle = Bundle().apply {
        putString("launch_source", "app_start")
        putLong("launch_time", System.currentTimeMillis())
        putString("debug_device", Build.MODEL)
        putString("app_version", packageInfo.versionName)
    }
    firebaseAnalytics.logEvent(FirebaseAnalytics.Event.APP_OPEN, launchBundle)

    CoroutineScope(Dispatchers.IO).launch {
        delay(1000)
        metricManager.log(
            SelectItem(
                refer = EventRefer.App,
                itemListName = EventItemListName.App,
                method = EventMethod.AppLaunch,
            )
        )
    }
}

private fun initializeAppsflyer(context: Context) {
    try {
        Timber.d("尝试初始化 AppFlyer...")
        val appsflyerManager = AppsflyerManager(context)
        Timber.d("成功创建 AppsflyerManager 实例")

        // 创建深链接回调
        val deepLinkCallback = object : DeepLinkCallback {
            override fun onDeepLinkReceived(deepLinkUri: Uri?, originalLink: String?) {
                // 使用统一的NavManager处理深链接
                Timber.d("navigate deep link: $deepLinkUri")
                Timber.d("original link: $originalLink")

                // 如果 deepLinkUri 不为空，则使用它进行导航
                if (deepLinkUri != null) {
                    NavManager.navigateDeepLink(deepLinkUri)
                } else {
                    Timber.w("deepLinkUri 为空，无法导航，originalLink: $originalLink")
                }

                // 上报 AppFlyer 事件
                CoroutineScope(Dispatchers.IO).launch {
                    try {
                        val eventValues = mutableMapOf<String, Any>()
                        eventValues["device_id"] = DeviceDataStoreManager.getDeviceId()

                        // 添加链接信息
                        when {
                            !originalLink.isNullOrEmpty() -> {
                                eventValues["url"] = originalLink
                            }

                            deepLinkUri != null -> {
                                eventValues["url"] = deepLinkUri.toString()
                            }

                            else -> {
                                eventValues["url"] = "empty"
                            }
                        }

                        val userId = UserDataStoreManager.getUserId()
                        if (userId != null) {
                            eventValues["user_id"] = userId
                        }

                        appsflyerManager.logEvent("af_open_with_link", eventValues)
                        Timber.d("AppFlyer 事件上报成功: af_open_with_link")
                    } catch (e: Exception) {
                        Timber.e(e, "AppFlyer 事件上报失败: ${e.message}")
                    }
                }
            }
        }

        // 设置深链接回调
        appsflyerManager.setDeepLinkCallback(deepLinkCallback)

        // 初始化AppFlyer
        appsflyerManager.init(BuildConfig.DEBUG)

        // 获取AppFlyer ID
        val afId = appsflyerManager.getAppsflyerId()
        afId?.let { MetricData.setAppsflyerId(it) }

        // 设置日志方法
        MetricData.setAppsflyerLogEvent(appsflyerManager, appsflyerManager::logEvent)

        Timber.d("AppFlyer 初始化完成")
    } catch (e: Exception) {
        Timber.e(e, "AppFlyer 初始化失败: ${e.message}")
    }
}
