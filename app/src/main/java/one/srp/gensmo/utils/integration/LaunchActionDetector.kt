package one.srp.gensmo.utils.integration

import android.content.Context
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map

private val Context.openActionDataStore by preferencesDataStore(name = "open_action_prefs")

object LaunchActionDetector {
    private fun makeKey(action: LaunchAction) =
        booleanPreferencesKey("has_launched_${action.value}")

    suspend fun checkFirstLaunch(context: Context, action: LaunchAction): <PERSON><PERSON>an {
        val key = makeKey(action)

        val hasLaunched = context.openActionDataStore.data.map { preferences ->
            preferences[key] == true
        }.first()

        return !hasLaunched
    }

    suspend fun recordFirstLaunch(context: Context, action: LaunchAction) {
        val key = makeKey(action)
        context.openActionDataStore.edit { preferences ->
            preferences[key] = true
        }
    }

    suspend fun resetFirstLaunch(context: Context, action: LaunchAction) {
        val key = makeKey(action)
        context.openActionDataStore.edit { preferences ->
            preferences.remove(key)
        }
    }
}

enum class LaunchAction(val value: String) {
    BOTTOM_NAV_BAR("bottom_nav_bar"),
    USER_CHECK_IN("user_check_in"),
    UNREGISTER_SESSION_CHAT("unregister_session_chat"),
    USER_ONBOARDING("user_onboarding"),
}