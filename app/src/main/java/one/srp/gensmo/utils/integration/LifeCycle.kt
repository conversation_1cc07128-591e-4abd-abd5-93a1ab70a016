package one.srp.gensmo.utils.integration

import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.ApplicationScope
import one.srp.gensmo.utils.metrics.MetricManager
import javax.inject.Inject
import javax.inject.Singleton


@Singleton
class AppLifecycleObserver @Inject constructor(
    @param:ApplicationScope private val scope: CoroutineScope,
    private val metricManager: MetricManager,
) : DefaultLifecycleObserver {

    override fun onStart(owner: LifecycleOwner) {
        scope.launch { foregroundTask(metricManager) }
    }

    override fun onStop(owner: LifecycleOwner) {
        scope.launch { backgroundTask(metricManager) }
    }
}

fun foregroundTask(metricManager: MetricManager) {
    metricManager.log(
        SelectItem(
            refer = EventRefer.App,
            itemListName = EventItemListName.App,
            method = EventMethod.AppForeground,
        )
    )
}

fun backgroundTask(metricManager: MetricManager) {
    metricManager.log(
        SelectItem(
            refer = EventRefer.App,
            itemListName = EventItemListName.App,
            method = EventMethod.AppBackground,
        )
    )
}
