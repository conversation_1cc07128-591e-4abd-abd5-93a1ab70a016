package one.srp.core.analytics.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import one.srp.core.analytics.AnalyticsManager
import one.srp.core.analytics.providers.MetricApiAnalyticsProvider
import one.srp.core.network.api.MetricApi
import javax.inject.Singleton

/**
 * Hilt module for analytics dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object AnalyticsModule {
    /**
     * Provides singleton instance of AnalyticsManager
     */
    @Provides
    @Singleton
    fun provideAnalyticsManager(): AnalyticsManager {
        return AnalyticsManager()
    }

    @Provides
    @Singleton
    fun provideMetricApiAnalytics(
        api: MetricApi,
    ): MetricApiAnalyticsProvider {
        return MetricApiAnalyticsProvider(api)
    }
}
