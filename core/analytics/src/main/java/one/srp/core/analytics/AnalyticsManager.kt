package one.srp.core.analytics

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import one.srp.core.analytics.events.MetricEvent
import one.srp.core.analytics.providers.AnalyticsProvider
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AnalyticsManager @Inject constructor() {

    private val providers = mutableListOf<AnalyticsProvider>()

    private val _isInitialized = MutableStateFlow(false)
    val isInitialized: StateFlow<Boolean> = _isInitialized.asStateFlow()

    private var initializing = false

    /**
     * Register an analytics provider
     */
    fun registerProvider(provider: AnalyticsProvider) {
        providers.add(provider)
        Timber.d("Registered analytics provider: ${provider.providerName}")
    }

    /**
     * Get all registered providers
     */
    fun getProviders(): List<AnalyticsProvider> = providers.toList()

    /**
     * Initialize the analytics manager and all registered providers
     */
    suspend fun initialize() {
        if (initializing || _isInitialized.value) return

        initializing = true

        try {
            providers.forEach { provider ->
                try {
                    provider.initialize()
                    Timber.d("[Metric] provider init done: ${provider.providerName}")
                } catch (e: Exception) {
                    Timber.w(e, "[Metric] provider init fail: ${provider.providerName}")
                }
            }
            _isInitialized.value = true
            initializing = false
            Timber.d("AnalyticsManager initialized with ${providers.size} providers")
        } catch (e: Exception) {
            initializing = false
            Timber.w(e, "Failed to initialize AnalyticsManager")
        }
    }

    /**
     * Track an analytics event across all providers
     */
    suspend fun logEvent(event: MetricEvent) {
        providers.forEach { provider ->
            try {
                if (provider.isInitialized) {
                    provider.logEvent(event)
                } else {
                    Timber.w("Provider ${provider.providerName} not initialized, skipping event ${event.eventName}")
                }
            } catch (e: Exception) {
                Timber.w(
                    e,
                    "Failed to track event ${event.eventName} via ${provider.providerName}"
                )
            }
        }
    }
}
