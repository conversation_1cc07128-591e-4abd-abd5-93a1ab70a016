package one.srp.core.analytics.events

import kotlinx.serialization.Serializable
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.core.analytics.types.METRIC_VERSION


@Serializable
data class MetricEvent(
    val eventName: String,
    val refer: EventRefer,

    val itemListName: EventItemListName,
    val items: List<EventItem> = emptyList(),

    val method: EventMethod? = null,
    val actionType: EventActionType? = null,

    val platform: String = "",
    val version: String = "",
    val eventVersion: String = METRIC_VERSION,

    val gensmoTimestamp: String = (System.currentTimeMillis() * 1000).toString(),
    val abInfo: String = "",
    val gensmoUserId: String = "",
    val gensmoUserType: String = "",
    val gensmoActiveId: String = "",
    val gensmoUserSourceId: String = "",
    val appsflyerId: String = "",
)

@Serializable
data class SelectItem(
    val eventName: String = "select_item",
    val refer: EventRefer,

    val itemListName: EventItemListName,
    val items: List<EventItem> = emptyList(),

    val method: EventMethod? = null,
    val actionType: EventActionType? = null,
)

@Serializable
data class ViewItem(
    val eventName: String = "view_item",
    val refer: EventRefer,

    val itemListName: EventItemListName,
    val items: List<EventItem>,
)


@Serializable
data class ViewItemList(
    val eventName: String = "view_item_list",
    val refer: EventRefer,

    val itemListName: EventItemListName,
    val items: List<EventItem>,
)
