package one.srp.core.analytics.utils

import android.os.Bundle
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import one.srp.core.analytics.events.MetricEvent
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.STRING_DEFAULT
import one.srp.core.network.utils.JSON


fun processStringForMetric(
    text: String?,
    limit: Int = 85,
    default: String = STRING_DEFAULT,
): String {
    return text?.let { text ->
        if (text.length <= limit) return text
        text.substring(0, limit)
    } ?: run {
        default
    }
}

fun processMetricEventToBundle(event: MetricEvent): Bundle = Bundle().apply {
    putString("event_name", event.eventName)
    putString("refer", event.refer.value)

    putString("item_list_name", event.itemListName.value)
    val bundles = event.items.map { item ->
        Bundle().apply {
            putString("item_category", item.itemCategory.value)
            item.itemId?.let { putString("item_id", processStringForMetric(it)) }
            item.itemName?.let { putString("item_name", processStringForMetric(it)) }
            putInt("index", item.index)
        }
    }
    putParcelableArrayList("items", ArrayList(bundles))

    event.method?.let { putString("method", it.value) }
    event.actionType?.let { putString("action_type", it.value) }

    putString("platform", event.platform)
    putString("version", event.version)
    putString("event_version", event.eventVersion)

    putString("gensmo_timestamp", event.gensmoTimestamp)
    putString("ab_info", event.abInfo)
    putString("gensmo_user_id", event.gensmoUserId)
    putString("gensmo_user_type", event.gensmoUserType)
    putString("gensmo_active_id", event.gensmoActiveId)
    putString("gensmo_user_source_id", event.gensmoUserSourceId)
    putString("appsflyer_id", event.appsflyerId)
}

fun processMetricEventToReqBody(event: MetricEvent): RequestBody {
    val procEvent = event.copy(items = event.items.map { it ->
        EventItem(
            itemCategory = it.itemCategory,
            itemId = processStringForMetric(it.itemId),
            itemName = processStringForMetric(it.itemName),
            index = it.index
        )
    })
    val jsonStr = JSON.encodeToString(procEvent)
    val reqBody = jsonStr.toRequestBody("application/json".toMediaTypeOrNull())
    return reqBody
}
